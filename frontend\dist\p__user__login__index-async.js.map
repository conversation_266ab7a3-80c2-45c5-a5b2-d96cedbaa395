{"version": 3, "sources": ["src/pages/user/login/index.tsx"], "sourcesContent": ["/**\n * 登录页面\n * 实现双阶段认证的第一阶段：账号登录\n */\n\nimport { MailOutlined, SafetyOutlined } from '@ant-design/icons';\nimport { Helmet, history, useModel } from '@umijs/max';\nimport {\n  Button,\n  Card,\n  Form,\n  Input,\n  message,\n  Space,\n  Typography,\n} from 'antd';\nimport { createStyles } from 'antd-style';\nimport React, { useState, useCallback, useMemo, useEffect } from 'react';\nimport { Footer } from '@/components';\nimport { AuthService } from '@/services';\nimport type { LoginRequest, SendVerificationCodeRequest } from '@/types/api';\nimport Settings from '../../../../config/defaultSettings';\n\nconst { Title, Text } = Typography;\n\n// 登录表单组件（移到外部避免重新创建）\nconst LoginFormComponent: React.FC<{\n  form: any;\n  handleLogin: (values: LoginRequest) => void;\n  handleSendCode: () => void;\n  sendingCode: boolean;\n  countdown: number;\n  loading: boolean;\n}> = React.memo(({ form, handleLogin, handleSendCode, sendingCode, countdown, loading }) => {\n  // 使用 useMemo 稳定按钮渲染，避免因倒计时变化导致输入框重新渲染\n  const sendCodeButton = useMemo(() => (\n    <Button\n      type=\"link\"\n      size=\"small\"\n      disabled={countdown > 0 || sendingCode}\n      loading={sendingCode}\n      onClick={handleSendCode}\n      style={{ padding: 0, height: 'auto' }}\n    >\n      {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}\n    </Button>\n  ), [countdown, sendingCode, handleSendCode]);\n\n  // 使用 useMemo 稳定邮箱输入框，避免重新渲染\n  const emailField = useMemo(() => (\n    <Form.Item\n      key=\"email-field\"\n      name=\"email\"\n      rules={[\n        { required: true, message: '请输入邮箱！' },\n        { type: 'email', message: '请输入有效的邮箱地址！' },\n      ]}\n    >\n      <Input\n        key=\"email-input\"\n        prefix={<MailOutlined />}\n        placeholder=\"邮箱\"\n        autoComplete=\"email\"\n      />\n    </Form.Item>\n  ), []);\n\n  // 使用 useMemo 稳定验证码输入框，只在按钮变化时重新渲染\n  const codeField = useMemo(() => (\n    <Form.Item\n      key=\"code-field\"\n      name=\"code\"\n      rules={[\n        { required: true, message: '请输入验证码！' },\n        { len: 6, message: '验证码为6位数字！' },\n        { pattern: /^\\d{6}$/, message: '验证码只能包含数字！' },\n      ]}\n    >\n      <Input\n        key=\"code-input\"\n        prefix={<SafetyOutlined />}\n        placeholder=\"6位验证码\"\n        maxLength={6}\n        suffix={sendCodeButton}\n      />\n    </Form.Item>\n  ), [sendCodeButton]);\n\n  return (\n    <Form\n      form={form}\n      name=\"login\"\n      size=\"large\"\n      onFinish={handleLogin}\n      autoComplete=\"off\"\n    >\n      {emailField}\n      {codeField}\n\n      {/* 提示信息 */}\n      <div style={{ marginBottom: 16, textAlign: 'center' }}>\n        <Text type=\"secondary\" style={{ fontSize: '12px' }}>\n          新用户将自动完成注册并登录\n        </Text>\n      </div>\n\n      <Form.Item>\n        <Button type=\"primary\" htmlType=\"submit\" loading={loading} block>\n          登录 / 注册\n        </Button>\n      </Form.Item>\n    </Form>\n  );\n});\n\nconst useStyles = createStyles(({ token }) => {\n  return {\n    container: {\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100vh',\n      overflow: 'auto',\n      backgroundImage:\n        \"url('https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/V-_oS6r-i7wAAAAAAAAAAAAAFl94AQBr')\",\n      backgroundSize: '100% 100%',\n    },\n    content: {\n      flex: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '32px 16px',\n    },\n    header: {\n      marginBottom: 40,\n      textAlign: 'center',\n    },\n    logo: {\n      marginBottom: 16,\n    },\n    title: {\n      marginBottom: 0,\n    },\n    loginCard: {\n      width: '100%',\n      maxWidth: 400,\n      boxShadow: token.boxShadowTertiary,\n    },\n    footer: {\n      marginTop: 40,\n      textAlign: 'center',\n    },\n    lang: {\n      width: 42,\n      height: 42,\n      lineHeight: '42px',\n      position: 'fixed',\n      right: 16,\n      top: 16,\n      borderRadius: token.borderRadius,\n      ':hover': {\n        backgroundColor: token.colorBgTextHover,\n      },\n    },\n  };\n});\n\nconst LoginPage: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [sendingCode, setSendingCode] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const [form] = Form.useForm(); // 将表单实例提升到父组件\n  const { styles } = useStyles();\n  const { setInitialState } = useModel('@@initialState');\n\n  // 使用 Form 内置的邮箱验证\n\n  // 组件挂载时清除倒计时状态，避免页面刷新后无法输入\n  useEffect(() => {\n    setCountdown(0);\n  }, []);\n\n  // 倒计时效果\n  React.useEffect(() => {\n    let timer: NodeJS.Timeout;\n    if (countdown > 0) {\n      timer = setTimeout(() => {\n        setCountdown(countdown - 1);\n      }, 1000);\n    }\n    return () => {\n      if (timer) clearTimeout(timer);\n    };\n  }, [countdown]);\n\n  // 发送验证码\n  const handleSendCode = useCallback(async (type: 'login' | 'register' = 'login') => {\n    let email: string;\n\n    try {\n      // 验证邮箱字段\n      await form.validateFields(['email']);\n\n      // 从表单获取邮箱值\n      email = form.getFieldValue('email');\n      console.log('发送验证码前的邮箱值:', email);\n\n      if (!email) {\n        message.error('请输入邮箱地址');\n        return;\n      }\n    } catch (error) {\n      // 表单验证失败\n      message.error('请输入有效的邮箱地址');\n      return;\n    }\n\n    setSendingCode(true);\n    try {\n      const request: SendVerificationCodeRequest = { email, type };\n      const response = await AuthService.sendVerificationCode(request);\n\n      if (response.success) {\n        message.success(response.message);\n        setCountdown(60); // 60秒倒计时\n\n        // 验证码发送成功后检查表单值\n        console.log('发送验证码成功后的邮箱值:', form.getFieldValue('email'));\n\n        // 在开发环境中提示查看控制台\n        if (process.env.NODE_ENV === 'development') {\n          message.info('开发环境：请查看浏览器控制台或后端日志获取验证码', 5);\n        }\n      } else {\n        message.error(response.message);\n        if (response.nextSendTime) {\n          setCountdown(response.nextSendTime);\n        }\n      }\n    } catch (error) {\n      console.error('发送验证码失败:', error);\n      message.error('发送验证码失败，请稍后重试');\n    } finally {\n      setSendingCode(false);\n    }\n  }, [form]);\n\n  // 处理登录/注册\n  const handleLogin = useCallback(async (values: LoginRequest) => {\n    setLoading(true);\n    try {\n      const response = await AuthService.login(values);\n      message.success('登录成功！');\n\n      // 登录成功后停止倒计时\n      setCountdown(0);\n\n      // 登录成功后，刷新 initialState 并等待完成\n      await setInitialState((prevState) => ({\n        ...prevState,\n        currentUser: response.user,\n        currentTeam: response.teams.length > 0 ? response.teams[0] : undefined,\n      }));\n\n      // 确保 initialState 更新完成后再进行页面跳转\n      // 使用 setTimeout 确保状态更新已经完成\n      setTimeout(() => {\n        // 根据团队数量进行不同的跳转处理\n        if (response.teams.length === 0) {\n          // 没有团队，跳转到个人中心页面\n          history.push('/personal-center');\n        } else {\n          // 有团队（无论一个还是多个），都跳转到个人中心整合页面\n          history.push('/personal-center', { teams: response.teams });\n        }\n      }, 100); // 给一个小的延迟确保状态更新完成\n    } catch (error) {\n      console.error('登录失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [setInitialState]);\n\n  // 注册功能已移除，统一使用验证码登录/注册流程\n\n  return (\n    <div className={styles.container}>\n      <Helmet>\n        <title>\n          登录 / 注册\n          {Settings.title && ` - ${Settings.title}`}\n        </title>\n      </Helmet>\n      <div className={styles.content}>\n        <div className={styles.header}>\n          <Space direction=\"vertical\" align=\"center\" size=\"large\">\n            <div className={styles.logo}>\n              <img src=\"/logo.svg\" alt=\"TeamAuth\" height={48} />\n            </div>\n            <div className={styles.title}>\n              <Title level={2}>团队管理系统</Title>\n              <Text type=\"secondary\">现代化的团队协作与管理平台</Text>\n            </div>\n          </Space>\n        </div>\n\n        <Card className={styles.loginCard}>\n          <LoginFormComponent\n            form={form}\n            handleLogin={handleLogin}\n            handleSendCode={() => handleSendCode('login')}\n            sendingCode={sendingCode}\n            countdown={countdown}\n            loading={loading}\n          />\n        </Card>\n\n        <div className={styles.footer}>\n          <Text type=\"secondary\">© 2025 TeamAuth. All rights reserved.</Text>\n        </div>\n      </div>\n      <Footer />\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;AAAA;;;CAGC;;;;4BAoUD;;;eAAA;;;;;;;8BAlU6C;4BACH;6BASnC;kCACsB;wEACoC;mCAC1C;iCACK;iFAEP;;;;;;;;;;;AAErB,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,qBAAqB;AACrB,MAAM,mCAOD,cAAK,CAAC,IAAI,IAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE;;IACrF,sCAAsC;IACtC,MAAM,iBAAiB,IAAA,cAAO,EAAC,kBAC7B,2BAAC,YAAM;YACL,MAAK;YACL,MAAK;YACL,UAAU,YAAY,KAAK;YAC3B,SAAS;YACT,SAAS;YACT,OAAO;gBAAE,SAAS;gBAAG,QAAQ;YAAO;sBAEnC,YAAY,IAAI,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG;;;;;kBAEvC;QAAC;QAAW;QAAa;KAAe;IAE3C,4BAA4B;IAC5B,MAAM,aAAa,IAAA,cAAO,EAAC,kBACzB,2BAAC,UAAI,CAAC,IAAI;YAER,MAAK;YACL,OAAO;gBACL;oBAAE,UAAU;oBAAM,SAAS;gBAAS;gBACpC;oBAAE,MAAM;oBAAS,SAAS;gBAAc;aACzC;sBAED,cAAA,2BAAC,WAAK;gBAEJ,sBAAQ,2BAAC,mBAAY;;;;;gBACrB,aAAY;gBACZ,cAAa;eAHT;;;;;WARF;;;;kBAcL,EAAE;IAEL,kCAAkC;IAClC,MAAM,YAAY,IAAA,cAAO,EAAC,kBACxB,2BAAC,UAAI,CAAC,IAAI;YAER,MAAK;YACL,OAAO;gBACL;oBAAE,UAAU;oBAAM,SAAS;gBAAU;gBACrC;oBAAE,KAAK;oBAAG,SAAS;gBAAY;gBAC/B;oBAAE,SAAS;oBAAW,SAAS;gBAAa;aAC7C;sBAED,cAAA,2BAAC,WAAK;gBAEJ,sBAAQ,2BAAC,qBAAc;;;;;gBACvB,aAAY;gBACZ,WAAW;gBACX,QAAQ;eAJJ;;;;;WATF;;;;kBAgBL;QAAC;KAAe;IAEnB,qBACE,2BAAC,UAAI;QACH,MAAM;QACN,MAAK;QACL,MAAK;QACL,UAAU;QACV,cAAa;;YAEZ;YACA;0BAGD,2BAAC;gBAAI,OAAO;oBAAE,cAAc;oBAAI,WAAW;gBAAS;0BAClD,cAAA,2BAAC;oBAAK,MAAK;oBAAY,OAAO;wBAAE,UAAU;oBAAO;8BAAG;;;;;;;;;;;0BAKtD,2BAAC,UAAI,CAAC,IAAI;0BACR,cAAA,2BAAC,YAAM;oBAAC,MAAK;oBAAU,UAAS;oBAAS,SAAS;oBAAS,KAAK;8BAAC;;;;;;;;;;;;;;;;;AAMzE;KAvFM;AAyFN,MAAM,YAAY,IAAA,uBAAY,EAAC,CAAC,EAAE,KAAK,EAAE;IACvC,OAAO;QACL,WAAW;YACT,SAAS;YACT,eAAe;YACf,QAAQ;YACR,UAAU;YACV,iBACE;YACF,gBAAgB;QAClB;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,cAAc;YACd,WAAW;QACb;QACA,MAAM;YACJ,cAAc;QAChB;QACA,OAAO;YACL,cAAc;QAChB;QACA,WAAW;YACT,OAAO;YACP,UAAU;YACV,WAAW,MAAM,iBAAiB;QACpC;QACA,QAAQ;YACN,WAAW;YACX,WAAW;QACb;QACA,MAAM;YACJ,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,OAAO;YACP,KAAK;YACL,cAAc,MAAM,YAAY;YAChC,UAAU;gBACR,iBAAiB,MAAM,gBAAgB;YACzC;QACF;IACF;AACF;AAEA,MAAM,YAAsB;;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,eAAQ,EAAC;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAC3C,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO,IAAI,cAAc;IAC7C,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,aAAQ,EAAC;IAErC,kBAAkB;IAElB,2BAA2B;IAC3B,IAAA,gBAAS,EAAC;QACR,aAAa;IACf,GAAG,EAAE;IAEL,QAAQ;IACR,cAAK,CAAC,SAAS,CAAC;QACd,IAAI;QACJ,IAAI,YAAY,GACd,QAAQ,WAAW;YACjB,aAAa,YAAY;QAC3B,GAAG;QAEL,OAAO;YACL,IAAI,OAAO,aAAa;QAC1B;IACF,GAAG;QAAC;KAAU;IAEd,QAAQ;IACR,MAAM,iBAAiB,IAAA,kBAAW,EAAC,OAAO,OAA6B,OAAO;QAC5E,IAAI;QAEJ,IAAI;YACF,SAAS;YACT,MAAM,KAAK,cAAc,CAAC;gBAAC;aAAQ;YAEnC,WAAW;YACX,QAAQ,KAAK,aAAa,CAAC;YAC3B,QAAQ,GAAG,CAAC,eAAe;YAE3B,IAAI,CAAC,OAAO;gBACV,aAAO,CAAC,KAAK,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,SAAS;YACT,aAAO,CAAC,KAAK,CAAC;YACd;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,UAAuC;gBAAE;gBAAO;YAAK;YAC3D,MAAM,WAAW,MAAM,qBAAW,CAAC,oBAAoB,CAAC;YAExD,IAAI,SAAS,OAAO,EAAE;gBACpB,aAAO,CAAC,OAAO,CAAC,SAAS,OAAO;gBAChC,aAAa,KAAK,SAAS;gBAE3B,gBAAgB;gBAChB,QAAQ,GAAG,CAAC,iBAAiB,KAAK,aAAa,CAAC;gBAI9C,aAAO,CAAC,IAAI,CAAC,4BAA4B;YAE7C,OAAO;gBACL,aAAO,CAAC,KAAK,CAAC,SAAS,OAAO;gBAC9B,IAAI,SAAS,YAAY,EACvB,aAAa,SAAS,YAAY;YAEtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,eAAe;QACjB;IACF,GAAG;QAAC;KAAK;IAET,UAAU;IACV,MAAM,cAAc,IAAA,kBAAW,EAAC,OAAO;QACrC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,qBAAW,CAAC,KAAK,CAAC;YACzC,aAAO,CAAC,OAAO,CAAC;YAEhB,aAAa;YACb,aAAa;YAEb,8BAA8B;YAC9B,MAAM,gBAAgB,CAAC,YAAe,CAAA;oBACpC,GAAG,SAAS;oBACZ,aAAa,SAAS,IAAI;oBAC1B,aAAa,SAAS,KAAK,CAAC,MAAM,GAAG,IAAI,SAAS,KAAK,CAAC,EAAE,GAAG;gBAC/D,CAAA;YAEA,+BAA+B;YAC/B,2BAA2B;YAC3B,WAAW;gBACT,kBAAkB;gBAClB,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,GAC5B,iBAAiB;gBACjB,YAAO,CAAC,IAAI,CAAC;qBAEb,6BAA6B;gBAC7B,YAAO,CAAC,IAAI,CAAC,oBAAoB;oBAAE,OAAO,SAAS,KAAK;gBAAC;YAE7D,GAAG,MAAM,kBAAkB;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAgB;IAEpB,yBAAyB;IAEzB,qBACE,2BAAC;QAAI,WAAW,OAAO,SAAS;;0BAC9B,2BAAC,WAAM;0BACL,cAAA,2BAAC;;wBAAM;wBAEJ,wBAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,wBAAQ,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;0BAG7C,2BAAC;gBAAI,WAAW,OAAO,OAAO;;kCAC5B,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,cAAA,2BAAC,WAAK;4BAAC,WAAU;4BAAW,OAAM;4BAAS,MAAK;;8CAC9C,2BAAC;oCAAI,WAAW,OAAO,IAAI;8CACzB,cAAA,2BAAC;wCAAI,KAAI;wCAAY,KAAI;wCAAW,QAAQ;;;;;;;;;;;8CAE9C,2BAAC;oCAAI,WAAW,OAAO,KAAK;;sDAC1B,2BAAC;4CAAM,OAAO;sDAAG;;;;;;sDACjB,2BAAC;4CAAK,MAAK;sDAAY;;;;;;;;;;;;;;;;;;;;;;;kCAK7B,2BAAC,UAAI;wBAAC,WAAW,OAAO,SAAS;kCAC/B,cAAA,2BAAC;4BACC,MAAM;4BACN,aAAa;4BACb,gBAAgB,IAAM,eAAe;4BACrC,aAAa;4BACb,WAAW;4BACX,SAAS;;;;;;;;;;;kCAIb,2BAAC;wBAAI,WAAW,OAAO,MAAM;kCAC3B,cAAA,2BAAC;4BAAK,MAAK;sCAAY;;;;;;;;;;;;;;;;;0BAG3B,2BAAC,kBAAM;;;;;;;;;;;AAGb;IA7JM;;QAIW,UAAI,CAAC;QACD;QACS,aAAQ;;;MANhC;IA+JN,WAAe"}