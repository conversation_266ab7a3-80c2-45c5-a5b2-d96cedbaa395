/**
 * 认证相关 API 服务
 */

import type {
  LoginRequest,
  LoginResponse,
  SendVerificationCodeRequest,
  SendVerificationCodeResponse
} from '@/types/api';
import { apiRequest, TokenManager } from '@/utils/request';

/**
 * 认证服务类（验证码登录系统）
 *
 * 提供基于验证码的用户认证系统，支持：
 * - 验证码发送和验证
 * - 用户登录（自动注册新用户）
 * - 团队选择和切换
 * - Token管理和刷新
 * - 用户登出和会话管理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
export class AuthService {
  /**
   * 发送验证码
   *
   * 向指定邮箱发送6位数字验证码，用于登录验证。
   * 如果邮箱未注册，系统会在登录时自动创建新用户。
   *
   * @param data 验证码发送请求参数
   * @param data.email 接收验证码的邮箱地址
   * @returns Promise<SendVerificationCodeResponse> 发送结果，包含成功状态和调试信息
   * @throws 当邮箱格式错误或发送频率过高时抛出异常
   *
   * @example
   * ```typescript
   * const response = await AuthService.sendVerificationCode({
   *   email: '<EMAIL>'
   * });
   *
   * if (response.success) {
   *   console.log('验证码发送成功');
   *   // 在开发环境中，验证码会自动填充到输入框
   * }
   * ```
   */
  static async sendVerificationCode(data: SendVerificationCodeRequest): Promise<SendVerificationCodeResponse> {
    const response = await apiRequest.post<SendVerificationCodeResponse>('/auth/send-code', data);

    // 在开发环境中输出验证码到控制台（用于调试）
    if (process.env.NODE_ENV === 'development' && response.data.success) {
      if (response.data.debugCode) {
        console.log('验证码:', response.data.debugCode);

        // 自动填充验证码（延迟1秒，确保用户能看到控制台输出）
        setTimeout(() => {
          const codeInput = document.querySelector('input[placeholder*="验证码"]') as HTMLInputElement;
          if (codeInput) {
            codeInput.value = response.data.debugCode!;
            codeInput.dispatchEvent(new Event('input', { bubbles: true }));
            codeInput.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('✅ 验证码已自动填充到输入框');
          }
        }, 1000);
      } else {
        console.log('验证码发送成功，请查看后端日志获取验证码');
      }
    }

    return response.data;
  }

  // 注册功能已移除，统一使用验证码登录/注册流程

  /**
   * 用户登录（验证码登录）
   *
   * 使用邮箱和验证码进行登录。如果邮箱未注册，系统会自动创建新用户。
   * 登录成功后返回用户信息和可选择的团队列表。
   *
   * @param data 登录请求参数
   * @param data.email 用户邮箱
   * @param data.code 6位数字验证码
   * @returns Promise<LoginResponse> 登录响应，包含用户信息、Token和团队列表
   * @throws 当验证码错误、已过期或邮箱格式错误时抛出异常
   *
   * @example
   * ```typescript
   * const loginResponse = await AuthService.login({
   *   email: '<EMAIL>',
   *   code: '123456'
   * });
   *
   * console.log('用户信息:', loginResponse.user);
   * console.log('可选团队:', loginResponse.teams);
   *
   * if (loginResponse.teams.length > 0) {
   *   // 需要选择团队
   *   await AuthService.selectTeam({ teamId: loginResponse.teams[0].id });
   * }
   * ```
   */
  static async login(data: LoginRequest): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>('/auth/login', data);

    // 保存用户Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 刷新Token
   *
   * 使用当前有效的Token获取新的Token，延长会话时间。
   * 通常在Token即将过期时自动调用。
   *
   * @returns Promise<LoginResponse> 包含新Token和用户信息的响应
   * @throws 当当前Token无效或已过期时抛出异常
   *
   * @example
   * ```typescript
   * try {
   *   const refreshResponse = await AuthService.refreshToken();
   *   console.log('Token已刷新');
   * } catch (error) {
   *   console.log('Token刷新失败，需要重新登录');
   *   // 跳转到登录页面
   * }
   * ```
   */
  static async refreshToken(): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>(
      '/auth/refresh-token',
    );

    // 更新Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 用户登出
   *
   * 清除服务器端的会话信息并清除本地Token。
   * 即使服务器请求失败，也会清除本地Token确保用户能够重新登录。
   *
   * @returns Promise<void> 登出完成时resolve
   *
   * @example
   * ```typescript
   * await AuthService.logout();
   * console.log('已登出');
   * // 页面会自动跳转到登录页面
   * ```
   */
  static async logout(): Promise<void> {
    try {
      await apiRequest.post<void>('/auth/logout');
    } finally {
      // 无论请求是否成功，都清除本地 token
      TokenManager.clearToken();
    }
  }

  /**
   * 验证Token有效性
   *
   * 向服务器验证当前Token是否仍然有效。
   * 用于检查用户会话状态，通常在应用启动时调用。
   *
   * @returns Promise<boolean> Token是否有效
   *
   * @example
   * ```typescript
   * const isValid = await AuthService.validateToken();
   * if (!isValid) {
   *   // Token无效，跳转到登录页面
   *   history.push('/user/login');
   * }
   * ```
   */
  static async validateToken(): Promise<boolean> {
    try {
      const response = await apiRequest.get<boolean>('/auth/validate');
      return response.data;
    } catch {
      return false;
    }
  }

  /**
   * 检查是否已登录
   *
   * 检查本地是否存储了有效的Token。
   * 注意：这只检查本地存储，不验证Token的服务器端有效性。
   *
   * @returns boolean 是否已登录（本地Token存在）
   *
   * @example
   * ```typescript
   * if (AuthService.isLoggedIn()) {
   *   console.log('用户已登录');
   * } else {
   *   console.log('用户未登录');
   * }
   * ```
   */
  static isLoggedIn(): boolean {
    return TokenManager.hasToken();
  }

  /**
   * 获取当前Token
   *
   * 从本地存储获取当前用户的Token。
   *
   * @returns string | null 当前Token，如果未登录则返回null
   *
   * @example
   * ```typescript
   * const token = AuthService.getToken();
   * if (token) {
   *   console.log('当前Token:', token);
   * }
   * ```
   */
  static getToken(): string | null {
    return TokenManager.getToken();
  }

  /**
   * 清除Token
   *
   * 清除本地存储的Token，但不通知服务器。
   * 通常用于强制登出或Token过期处理。
   *
   * @example
   * ```typescript
   * AuthService.clearToken();
   * console.log('Token已清除');
   * ```
   */
  static clearToken(): void {
    TokenManager.clearToken();
  }

  /**
   * 清除团队Token（兼容性方法）
   *
   * 在单Token系统中，清除Token即可。
   *
   * @deprecated 使用 clearToken() 替代
   */
  static clearTeamToken(): void {
    // 在单令牌系统中，清除Token即可
    TokenManager.clearToken();
  }

  /**
   * 选择团队
   *
   * 在用户登录后选择要操作的团队。
   * 选择团队后，Token会更新为包含团队上下文的新Token。
   *
   * @param data 团队选择请求参数
   * @param data.teamId 要选择的团队ID
   * @returns Promise<LoginResponse> 包含新Token和团队信息的响应
   * @throws 当团队不存在或用户无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const teamResponse = await AuthService.selectTeam({
   *   teamId: 123
   * });
   *
   * console.log('当前团队:', teamResponse.currentTeam);
   * console.log('团队Token已更新');
   * ```
   */
  static async selectTeam(data: { teamId: number }): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>(
      '/auth/select-team',
      data,
    );

    // 更新Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 切换团队
   *
   * 在已选择团队的情况下切换到另一个团队。
   * 与selectTeam功能相同，但语义上表示从一个团队切换到另一个团队。
   *
   * @param data 团队切换请求参数
   * @param data.teamId 要切换到的团队ID
   * @returns Promise<LoginResponse> 包含新Token和团队信息的响应
   * @throws 当团队不存在或用户无权限访问时抛出异常
   *
   * @example
   * ```typescript
   * const switchResponse = await AuthService.switchTeam({
   *   teamId: 456
   * });
   *
   * console.log('已切换到团队:', switchResponse.currentTeam?.name);
   * ```
   */
  static async switchTeam(data: { teamId: number }): Promise<LoginResponse> {
    const response = await apiRequest.post<LoginResponse>(
      '/auth/switch-team',
      data,
    );

    // 更新Token
    if (response.data.token) {
      TokenManager.setToken(response.data.token);
    }

    return response.data;
  }

  /**
   * 清除团队上下文
   *
   * 清除当前选择的团队，返回到用户级别的Token。
   * 用户可以重新选择团队或进行用户级别的操作。
   *
   * @returns Promise<string> 新的用户级别Token
   * @throws 当用户未登录时抛出异常
   *
   * @example
   * ```typescript
   * const userToken = await AuthService.clearTeam();
   * console.log('已清除团队上下文，返回用户级别');
   * ```
   */
  static async clearTeam(): Promise<string> {
    const response = await apiRequest.post<string>('/auth/clear-team');

    // 更新Token
    if (response.data) {
      TokenManager.setToken(response.data);
    }

    return response.data;
  }

  // ========== 兼容性方法 ==========

  /**
   * 检查是否已选择团队（兼容性方法）
   * @deprecated 在单令牌系统中，团队信息包含在Token中，使用 isLoggedIn 检查登录状态
   */
  static hasTeamSelected(): boolean {
    return AuthService.isLoggedIn();
  }
  /**
   * 团队登录（兼容性方法）
   * @deprecated 使用 selectTeam 替代
   */
  static async teamLogin(data: { teamId: number }): Promise<LoginResponse> {
    return AuthService.selectTeam(data);
  }

  /**
   * 清除所有Token（兼容性方法）
   * @deprecated 使用 clearToken 替代
   */
  static clearTokens(): void {
    AuthService.clearToken();
  }
}

// 导出默认实例
export default AuthService;
